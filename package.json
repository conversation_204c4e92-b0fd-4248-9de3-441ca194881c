{"name": "websocket-ai-automation-test-agentq", "version": "1.0.0", "private": true, "scripts": {"start": "node dist/server.js", "start2": "node dist/test-runner-testrun.js", "dev": "concurrently \"npm run dev:server1\" \"npm run dev:testrun\"", "dev:server1": "nodemon --exec 'ts-node src/server.ts'", "dev:testrun": "nodemon --exec 'ts-node src/test-runner-testrun.ts'", "testrun": "ts-node src/test-runner-testrun.ts", "build": "tsc"}, "dependencies": {"@google/generative-ai": "^0.24.0", "agentq_web_automation_test": "^1.0.4", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "commander": "^13.0.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "form-data": "^4.0.3", "jsonwebtoken": "^9.0.2", "luxon": "^3.5.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "openai": "^4.20.1", "ts-node": "^10.9.1", "typescript": "^5.2.2", "ws": "^8.14.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/luxon": "^3.4.2", "@types/multer": "^1.4.12", "@types/node": "^20.9.1", "@types/ws": "^8.5.10", "concurrently": "^8.2.2", "nodemon": "^3.0.1"}}