<template>
    <div v-if="show" class="modal-overlay">
      <div class="modal-content" :class="{ 'bulk-update-modal': isBulkUpdate }">
        <div class="modal-header">
          <h3 class="modal-title">{{ isBulkUpdate ? `Bulk Update (${selectedTestResults?.length || 0} Test Results)` : 'Test Case History' }}</h3>
          <div class="header-actions">
          </div>
          <button class="close-button" @click="closeModal">
            <span class="material-icons">x</span>
          </button>
        </div>

        <div class="modal-body">
          <div v-if="loading" class="loading">
            Loading...
          </div>

          <div v-else-if="error" class="error-message">
            {{ error }}
          </div>

          <div v-else-if="isBulkUpdate" class="bulk-update-container">
            <div class="bulk-update-header">
              <h4>Bulk Update {{ selectedTestResults?.length || 0 }} Test Results</h4>
              <p class="bulk-update-description">
                Update the status, actual result, and notes for all selected test results at once.
              </p>
            </div>

            <div class="bulk-update-form">
              <div class="form-group">
                <label for="status-select">Status</label>
                <select id="status-select" v-model="editableStatus" class="status-select" :class="{ 'status-warning-border': editableStatus === 'untested' || !editableStatus }">
                  <option value="" disabled>Select a status</option>
                  <option value="passed">Passed</option>
                  <option value="failed">Failed</option>
                  <option value="blocked">Blocked</option>
                  <option value="skipped">Skipped</option>
                </select>
              </div>

              <div class="form-group">
                <label for="actual-result">Actual Result</label>
                <textarea
                  id="actual-result"
                  v-model="editableActualResult"
                  class="edit-textarea"
                  placeholder="Enter actual result for all selected test results"
                ></textarea>
              </div>

              <div class="form-group">
                <label for="notes">Notes</label>
                <textarea
                  id="notes"
                  v-model="editableNotes"
                  class="edit-textarea"
                  placeholder="Enter notes for all selected test results"
                ></textarea>
              </div>

              <div class="edit-actions">
                <button
                  class="save-button"
                  @click="saveChanges"
                  :disabled="editableStatus === 'untested' || !editableStatus"
                  :class="{ 'disabled-button': editableStatus === 'untested' || !editableStatus }"
                >
                  <span class="save-icon">💾</span> Save Changes
                </button>
                <button class="cancel-button" @click="closeModal">
                  Cancel
                </button>
              </div>
            </div>
          </div>

          <div v-else class="three-column-layout">
            <!-- Left Column: Test Case Details -->
            <div class="test-case-details-column">
              <div class="test-case-info">
                <h4 class="test-case-title">
                  TC-{{ tcId }} {{ testCaseTitle }}
                </h4>
                <div class="test-case-properties">
                  <div class="test-case-property">
                    <strong>Precondition:</strong>
                    <p v-html="formatSteps(testCasePrecondition)"></p>
                  </div>
                  <div class="test-case-property">
                    <strong>Steps:</strong>
                    <p v-html="formatSteps(testCaseSteps)"></p>
                  </div>
                  <div class="test-case-property">
                    <strong>Expectation:</strong>
                    <p v-html="formatSteps(testCaseExpectation)"></p>
                  </div>
                  <div class="test-case-property">
                    <strong>Type:</strong>
                    <span>{{ tcType }}</span>
                  </div>
                  <div class="test-case-property">
                    <strong>Priority:</strong>
                    <span>{{ testCasePriority }}</span>
                  </div>
                  <div class="test-case-property">
                    <strong>Platform:</strong>
                    <span>{{ testCasePlatform }}</span>
                  </div>
                  <div class="test-case-property">
                    <strong>Test Case Type:</strong>
                    <span>{{ testCaseType }}</span>
                  </div>
                  <div class="test-case-property">
                    <strong>Tags:</strong>
                    <span v-if="testCaseTags && testCaseTags.length > 0">
                      <span
                        v-for="tag in testCaseTags"
                        :key="tag.id"
                        class="tag-badge"
                      >
                        {{ tag.name }}
                      </span>
                    </span>
                    <span v-else>-</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Middle Column: History List -->
            <div class="history-column">
              <div class="previous-changes-header">
                <div class="history-section-title">
                  <h4>Test Result History</h4>
                </div>

                <!-- Item count indicator -->
                <div class="history-count" v-if="totalItems > 0">
                  {{ totalItems }} result{{ totalItems !== 1 ? 's' : '' }}
                </div>
              </div>

              <div v-if="history.length === 0 && totalItems === 0" class="empty-state">
                No history available for this test case.
              </div>

              <!-- History Items -->
              <div class="history-items-list">
                <div v-for="item in history" :key="item.id" class="history-item" :class="{ 'current-item': item.isLatest }">
                  <div class="history-item-content">
                    <!-- Left border indicator for status -->
                    <div :class="['status-indicator', getStatusClass(item.status)]"></div>

                    <!-- Main content area -->
                    <div class="history-item-main">
                      <!-- Header with status and timestamp -->
                      <div class="history-header">
                        <span
                          :class="['status-badge', getStatusClass(item.status)]"
                        >
                          {{ item.status }}
                        </span>
                        <span class="timestamp">
                          {{ new Date(item.createdAt).toLocaleString(undefined, {
                            year: 'numeric',
                            month: 'numeric',
                            day: 'numeric',
                            hour: 'numeric',
                            minute: 'numeric',
                            hour12: true
                          }) }}
                        </span>
                      </div>

                      <!-- Details section -->
                      <div class="history-details">
                        <div class="detail-group">
                          <label>Actual Result:</label>
                          <div class="content-container">
                            <p class="truncated-content" :class="{ 'empty-content': !item.actualResult }">
                              {{ cleanText(item.actualResult) || 'None' }}
                            </p>
                            <button v-if="item.actualResult && cleanText(item.actualResult).length > 100"
                              class="show-more-button"
                              @click="toggleShowMore($event, 'actualResult')">
                              Show more
                            </button>
                          </div>
                        </div>

                        <div class="detail-group">
                          <label>Notes:</label>
                          <div class="content-container">
                            <p class="truncated-content" :class="{ 'empty-content': !item.notes }">
                              {{ cleanText(item.notes) || 'None' }}
                            </p>
                            <button v-if="item.notes && cleanText(item.notes).length > 100"
                              class="show-more-button"
                              @click="toggleShowMore($event, 'notes')">
                              Show more
                            </button>
                          </div>

                          <!-- Artifact Links -->
                          <div v-if="item.videoUrl || item.screenshotUrl" class="artifact-links">
                            <button
                              v-if="item.screenshotUrl"
                              class="artifact-link screenshot-link"
                              @click="showScreenshot(item.screenshotUrl)"
                            >
                              📸 Screenshot
                            </button>
                            <button
                              v-if="item.videoUrl"
                              class="artifact-link video-link"
                              @click="showVideo(item.videoUrl)"
                            >
                              🎥 Video
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Single pagination control at the bottom -->
                <div v-if="totalPages > 1" class="pagination-container sticky-pagination">
                  <div class="pagination-controls">
                    <button
                      class="pagination-button"
                      :disabled="currentPage === 1"
                      @click="goToPage(1)"
                      title="First Page"
                    >
                      «
                    </button>
                    <button
                      class="pagination-button"
                      :disabled="currentPage === 1"
                      @click="goToPage(currentPage - 1)"
                      title="Previous Page"
                    >
                      ‹
                    </button>

                    <div class="pagination-pages">
                      <template v-for="page in displayedPages" :key="page">
                        <span v-if="page === '...'" class="pagination-ellipsis">...</span>
                        <button
                          v-else
                          class="pagination-page-button"
                          :class="{ active: page === currentPage }"
                          @click="goToPage(page as number)"
                        >
                          {{ page }}
                        </button>
                      </template>
                    </div>

                    <button
                      class="pagination-button"
                      :disabled="currentPage === totalPages"
                      @click="goToPage(currentPage + 1)"
                      title="Next Page"
                    >
                      ›
                    </button>
                    <button
                      class="pagination-button"
                      :disabled="currentPage === totalPages"
                      @click="goToPage(totalPages)"
                      title="Last Page"
                    >
                      »
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Column: Update Test Result -->
            <div class="update-result-column">
              <div class="current-result-header">
                <h4>Update Test Result</h4>
                <button
                  v-if="!isEditing"
                  class="edit-button"
                  @click="startEditing"
                >
                  <span class="edit-icon">✏️</span> Update Result
                </button>
              </div>

              <div class="current-result">
                <div v-if="!isEditing" class="result-view-mode">
                  <div class="result-card">
                    <div class="result-row">
                      <div class="result-label">Last Status</div>
                      <div class="result-value">
                        <span :class="['status-badge', getStatusClass(localStatus)]">
                          {{ localStatus }}
                        </span>
                      </div>
                    </div>

                    <div class="result-row" v-if="localStatus === 'failed'">
                      <div class="result-label">Defect</div>
                      <div class="result-value">
                        <span v-if="defectIssue" class="defect-link">
                          <span v-if="defectIssue.defectId" class="defect-id">D{{ defectIssue.defectId }}</span>
                          <a :href="defectIssue.jiraIssueUrl" target="_blank" rel="noopener noreferrer">
                            {{ defectIssue.jiraIssueKey }}
                          </a>
                        </span>
                        <button
                          v-else
                          class="create-defect-button"
                          @click="showCreateJiraModal = true"
                        >
                          <span class="defect-icon">🐞</span> Create Defect
                        </button>
                      </div>
                    </div>

                    <div class="result-row">
                      <div class="result-label">Last Updated</div>
                      <div class="result-value timestamp">
                        {{ testCaseUpdatedAt ?
                          new Date(testCaseUpdatedAt).toLocaleString(undefined, {
                            year: 'numeric',
                            month: 'numeric',
                            day: 'numeric',
                            hour: 'numeric',
                            minute: 'numeric',
                            hour12: true,
                            timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
                          }) :
                          new Date(testCaseCreatedAt).toLocaleString(undefined, {
                            year: 'numeric',
                            month: 'numeric',
                            day: 'numeric',
                            hour: 'numeric',
                            minute: 'numeric',
                            hour12: true,
                            timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
                          }) }}
                      </div>
                    </div>

                    <div class="result-row">
                      <div class="result-label">Actual Result</div>
                      <div class="result-value result-text">
                        {{ cleanText(localActualResult) || '-' }}
                      </div>
                    </div>

                    <div class="result-row">
                      <div class="result-label">Notes</div>
                      <div class="result-value result-text">
                        {{ cleanText(localNotes) || '-' }}
                      </div>
                    </div>

                    <!-- Artifact Links for Current Result -->
                    <div v-if="props.testCaseVideoUrl || props.testCaseScreenshotUrl" class="result-row">
                      <div class="result-label">Artifacts</div>
                      <div class="result-value">
                        <div class="artifact-links">
                          <button
                            v-if="props.testCaseScreenshotUrl"
                            class="artifact-link screenshot-link"
                            @click="showScreenshot(props.testCaseScreenshotUrl)"
                          >
                            📸 Screenshot
                          </button>
                          <button
                            v-if="props.testCaseVideoUrl"
                            class="artifact-link video-link"
                            @click="showVideo(props.testCaseVideoUrl)"
                          >
                            🎥 Video
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div v-else class="result-edit-mode">
                  <div class="edit-form">
                    <div class="form-group">
                      <label for="status-select">Status</label>
                      <select id="status-select" v-model="editableStatus" class="status-select" :class="{ 'status-warning-border': editableStatus === 'untested' || !editableStatus }">
                        <option value="" disabled>Select a status</option>
                        <option value="passed">Passed</option>
                        <option value="failed">Failed</option>
                        <option value="blocked">Blocked</option>
                        <option value="skipped">Skipped</option>
                      </select>
                    </div>

                    <div class="form-group">
                      <label for="actual-result">Actual Result</label>
                      <textarea
                        id="actual-result"
                        v-model="editableActualResult"
                        class="edit-textarea"
                        placeholder="Enter actual result"
                      ></textarea>
                    </div>

                    <div class="form-group">
                      <label for="notes">Notes</label>
                      <textarea
                        id="notes"
                        v-model="editableNotes"
                        class="edit-textarea"
                        placeholder="Enter notes"
                      ></textarea>
                    </div>

                    <div class="edit-actions">
                      <button
                        class="save-button"
                        @click="saveChanges"
                        :disabled="editableStatus === 'untested' || !editableStatus"
                        :class="{ 'disabled-button': editableStatus === 'untested' || !editableStatus }"
                      >
                        <span class="save-icon">💾</span> Save Changes
                      </button>
                      <button class="cancel-button" @click="cancelEditing">
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <CreateJiraIssueModal
        v-if="showCreateJiraModal"
        :show="showCreateJiraModal"
        :test-case-title="testCaseTitle"
        :test-case-steps="testCaseSteps"
        :test-case-expectation="testCaseExpectation"
        :test-case-actual-result="localActualResult"
        :test-case-notes="localNotes"
        :test-case-screenshot-url="testCaseScreenshotUrl"
        :test-case-video-url="testCaseVideoUrl"
        :project-id="projectId"
        :test-run-id="testRunId"
        :test-result-id="testResultId"
        @close="showCreateJiraModal = false"
        @created="handleDefectCreated"
      />

      <!-- Artifact Modal -->
      <div v-if="showArtifactModal" class="artifact-modal-overlay" @click="closeArtifactModal">
        <div class="artifact-modal-content" @click.stop>
          <div class="artifact-modal-header">
            <h3>{{ currentArtifact?.type === 'screenshot' ? '📸 Screenshot' : '🎥 Video' }}</h3>
            <button class="artifact-close-button" @click="closeArtifactModal">
              <span class="material-icons">×</span>
            </button>
          </div>
          <div class="artifact-modal-body">
            <img
              v-if="currentArtifact?.type === 'screenshot'"
              :src="currentArtifact.url"
              alt="Test Screenshot"
              class="artifact-screenshot"
            />
            <video
              v-if="currentArtifact?.type === 'video'"
              :src="currentArtifact.url"
              controls
              class="artifact-video"
            >
              Your browser does not support the video tag.
            </video>
          </div>
        </div>
      </div>
    </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch, onUnmounted, computed } from 'vue';
  import axios from 'axios';
  import CreateJiraIssueModal from './CreateJiraIssueModal.vue';

  interface TestResult {
    id: string;
    status: string;
    actualResult: string | null;
    notes: string | null;
    createdAt: string;
    sequence: number;
    isLatest: boolean;
    videoUrl?: string | null;
    screenshotUrl?: string | null;
  }

  interface Tag {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
  }

  const props = defineProps<{
    show: boolean;
    projectId: string;
    testRunId: string;
    testResultId: string;
    testCaseTitle: string;
    testCaseType: string;
    tcType: string;
    testCasePriority: string;
    testCasePlatform: string;
    testCaseTags: Array<Tag>;
    testCasePrecondition: string;
    testCaseSteps: string;
    testCaseExpectation: string;
    tcId: string | number;
    testCaseStatus: string;
    testCaseNotes: string | null;
    testCaseActualResult: string | null;
    testCaseUpdatedAt: string;
    testCaseCreatedAt: string;
    testCaseScreenshotUrl: string | null;
    testCaseVideoUrl: string | null;
    isBulkUpdate?: boolean;
    selectedTestResults?: TestResult[];
  }>();

  const emit = defineEmits<{
    'close': [];
    'updated': [updatedData: {
      status: string;
      actualResult: string | null;
      notes: string | null;
    }];
    'refresh': [];
  }>();

  const history = ref<TestResult[]>([]);
  const showCreateJiraModal = ref(false);
  const defectIssue = ref<{
    jiraIssueKey: string;
    jiraIssueUrl: string;
    defectId: number;
    testRunId: string;
  } | null>(null);

  const summaryData = ref({
    title: props.testCaseTitle,
    type: props.testCaseType,
  })
  const loading = ref(false);
  const error = ref('');
  const isEditing = ref(false);
  const editableStatus = ref('');
  const editableActualResult = ref<string | null>('');
  const editableNotes = ref<string | null>('');
  const projectId = props.projectId;
  const testRunId = props.testRunId;
  // Use ref for testResultId so it can be updated when a new result is created
  const testResultId = ref(props.testResultId);
  const localStatus = ref(props.testCaseStatus);
  const localActualResult = ref(props.testCaseActualResult);
  const localNotes = ref(props.testCaseNotes);
  const localUpdatedAt = ref(props.testCaseUpdatedAt);

  // Popup state for artifacts
  const showArtifactModal = ref(false);
  const currentArtifact = ref<{
    type: 'screenshot' | 'video';
    url: string;
  } | null>(null);

  // Pagination
  const currentPage = ref(1);
  const pageSize = ref(2); // Number of history items per page
  const totalItems = ref(0);
  const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));

  // Calculate which page numbers to display (show max 3 pages with ellipsis)
  const displayedPages = computed(() => {
    if (totalPages.value <= 3) {
      // If 3 or fewer pages, show all
      return Array.from({ length: totalPages.value }, (_, i) => i + 1);
    }

    // Always show current page
    const pages: (number | string)[] = [currentPage.value];

    // Add one page before current if possible
    if (currentPage.value > 1) {
      pages.unshift(currentPage.value - 1);
    }

    // Add one page after current if possible
    if (currentPage.value < totalPages.value) {
      pages.push(currentPage.value + 1);
    }

    // If we have room for one more page and we're not at the start
    if (pages.length < 3 && currentPage.value > 2) {
      pages.unshift(currentPage.value - 2);
    }

    // If we have room for one more page and we're not at the end
    if (pages.length < 3 && currentPage.value < totalPages.value - 1) {
      pages.push(currentPage.value + 2);
    }

    // Add ellipsis at the beginning if needed
    if (typeof pages[0] === 'number' && pages[0] > 1) {
      pages.unshift('...');
    }

    // Add ellipsis at the end if needed
    const lastPage = pages[pages.length - 1];
    if (typeof lastPage === 'number' && lastPage < totalPages.value) {
      pages.push('...');
    }

    return pages;
  });

  const startEditing = () => {
    isEditing.value = true;
    editableStatus.value = localStatus.value;
    editableActualResult.value = localActualResult.value;
    editableNotes.value = localNotes.value;
  };

  const cancelEditing = () => {
    isEditing.value = false;
  };

  const closeModal = () => {
    emit('close');
    emit('refresh');
  };

  const handleDefectCreated = async (_issueKey: string) => {
    try {
      const response = await axios.get(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/test-results/${testResultId.value}/jira-issue`
      );
      defectIssue.value = response.data;
      showCreateJiraModal.value = false;
    } catch (err) {
      console.error('Failed to fetch defect details:', err);
    }
  };

  const saveChanges = async () => {
    if (!editableStatus.value) {
      error.value = 'Please select a status before saving.';
      return;
    }

    try {
      loading.value = true;
      error.value = ''; // Clear any previous errors

      if (props.isBulkUpdate && props.selectedTestResults && props.selectedTestResults.length > 0) {
        // Bulk update - create an array of test result IDs
        const testResultIds = props.selectedTestResults.map(result => result.id);

        // Log the request URL and payload for debugging
        const url = `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/test-results/bulk-update`;
        const payload = {
          testResultIds,
          status: editableStatus.value,
          actualResult: editableActualResult.value,
          notes: editableNotes.value
        };

        console.log('Bulk update request URL:', url);
        console.log('Bulk update payload:', payload);

        try {
          // Get the auth token from localStorage
          const token = localStorage.getItem('token');

          // Call the bulk update endpoint with auth header
          const response = await axios.post(url, payload, {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });
          console.log('Bulk update response:', response.data);
        } catch (err) {
          console.error('Bulk update error:', err);
          throw err; // Re-throw to be caught by the outer try/catch
        }

        // Update local refs with new values
        localStatus.value = editableStatus.value;
        localActualResult.value = editableActualResult.value;
        localNotes.value = editableNotes.value;
        localUpdatedAt.value = new Date().toISOString();
      } else {
        // Single test result update
        const response = await axios.patch(
          `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/test-results/${testResultId.value}`,
          {
            status: editableStatus.value,
            actualResult: editableActualResult.value,
            notes: editableNotes.value
          }
        );

        // Get the new test result from the response
        const newTestResult = response.data;

        // Update local refs with new values from the new test result
        localStatus.value = newTestResult.status;
        localActualResult.value = newTestResult.actualResult;
        localNotes.value = newTestResult.notes;
        localUpdatedAt.value = newTestResult.createdAt;

        // Update testResultId to point to the latest result
        testResultId.value = newTestResult.id;

        // Refresh history - reset to first page
        await fetchHistory(1);
      }

      await fetchSummary();

      // Emit updated data to parent
      emit('updated', {
        status: localStatus.value,
        actualResult: localActualResult.value,
        notes: localNotes.value
      });

      emit('refresh');
      isEditing.value = false;
    } catch (err) {
      console.error('Error updating test result:', err);
      error.value = err.response?.data?.message || 'Failed to update test result';
    } finally {
      loading.value = false;
    }
  };

  const fetchHistory = async (page = 1) => {
    if (!props.show || !props.testResultId) {
      return; // Don't fetch if not shown or no ID
    }

    try {
      loading.value = true;
      error.value = '';
      history.value = []; // Clear previous history
      currentPage.value = page;

      // Fetch all history items first to get the total count
      // In a production environment, this should be handled by the backend with proper pagination
      const response = await axios.get(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${props.projectId}/test-runs/${props.testRunId}/test-results/${testResultId.value}/history`
      );

      // Get history data - all test results for this test case in this test run
      const allHistory = response.data as TestResult[];

      // Store the total number of items for pagination
      totalItems.value = allHistory.length;

      // Calculate start and end indices for the current page
      const startIndex = (currentPage.value - 1) * pageSize.value;
      const endIndex = Math.min(startIndex + pageSize.value, allHistory.length);

      // Get only the items for the current page
      history.value = allHistory.slice(startIndex, endIndex);
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch history';
    } finally {
      loading.value = false;
    }
  };

  const goToPage = (page: number) => {
    if (page < 1 || page > totalPages.value) return;
    fetchHistory(page);
  };

  const fetchSummary = async () => {
    try {
      const response = await axios.get(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${props.testRunId}/summary`
      );
      summaryData.value = response.data;
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch summary data';
    }
  };

  const getStatusClass = (status: string) => {
    const classes = {
      passed: 'status-passed',
      failed: 'status-failed',
      blocked: 'status-blocked',
      skipped: 'status-skipped',
    };
    return classes[status as keyof typeof classes] || '';
  };

  const formatSteps = (steps: string) => {
    // Example: Replace newlines with <br> for HTML rendering
    return steps.replace(/\n/g, '<br>');
  };

  const toggleShowMore = (event: Event, _type: string) => {
    // Get the parent content container
    const button = event.target as HTMLElement;
    const contentContainer = button.closest('.content-container');

    if (contentContainer) {
      // Toggle the expanded class on the content paragraph
      const content = contentContainer.querySelector('.truncated-content');
      if (content) {
        content.classList.toggle('expanded');
      }

      // Update button text
      if (button.textContent?.includes('Show more')) {
        button.textContent = 'Show less';
      } else {
        button.textContent = 'Show more';
      }
    }
  };

  // Clean text function to remove ANSI color codes and format nicely
  const cleanText = (text: string | null): string => {
    if (!text) return '';

    // Remove ANSI color codes and escape sequences
    return text
      .replace(/\x1b\[[0-9;]*m/g, '') // Remove ANSI color codes
      .replace(/\[31m|\[39m|\[2m|\[22m|\[32m|\[7m|\[27m/g, '') // Remove specific color codes
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim(); // Remove leading/trailing whitespace
  };

  // Show screenshot in popup
  const showScreenshot = async (screenshotUrl: string) => {
    try {
      // Get signed URL from backend
      const response = await axios.get(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/storage/signed-url`,
        {
          params: { gcsUrl: screenshotUrl }
        }
      );

      currentArtifact.value = {
        type: 'screenshot',
        url: response.data.signedUrl
      };
      showArtifactModal.value = true;
    } catch (error) {
      console.error('Failed to get screenshot URL:', error);
      alert('Failed to load screenshot');
    }
  };

  // Show video in popup
  const showVideo = async (videoUrl: string) => {
    try {
      // Get signed URL from backend
      const response = await axios.get(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/storage/signed-url`,
        {
          params: { gcsUrl: videoUrl }
        }
      );

      currentArtifact.value = {
        type: 'video',
        url: response.data.signedUrl
      };
      showArtifactModal.value = true;
    } catch (error) {
      console.error('Failed to get video URL:', error);
      alert('Failed to load video');
    }
  };

  // Close artifact modal
  const closeArtifactModal = () => {
    showArtifactModal.value = false;
    currentArtifact.value = null;
  };

  watch(
    () => ({
      show: props.show,
      testResultId: props.testResultId,
      isBulkUpdate: props.isBulkUpdate
    }),
    (newValues) => {
      if (newValues.show && newValues.testResultId) {
        // Reset to first page when showing the modal
        currentPage.value = 1;

        // For bulk updates, always start with empty status to force user selection
        if (newValues.isBulkUpdate) {
          editableStatus.value = '';
        }

        fetchHistory(1);
      }
    }
  );

  const handleEsc = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      emit('close');
      emit('refresh');
    }
  };

  onUnmounted(() => {
    document.removeEventListener('keydown', handleEsc);
  });

  onMounted(async () => {
    document.addEventListener('keydown', handleEsc);
    // Initial fetch if show and testResultId are already set
    if (props.show && props.testResultId) {
      // Start with page 1
      fetchHistory(1);
      fetchSummary();
    }

    // Fetch defect issue if it exists
    try {
      const response = await axios.get(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/test-results/${testResultId.value}/jira-issue`
      );
      defectIssue.value = response.data;
    } catch (err) {
      // Ignore 404 errors (no defect exists)
      if (err.response?.status !== 404) {
        console.error('Failed to fetch defect details:', err);
      }
    }
  });
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  overflow: auto; /* Enable scrolling if content is too large */
  backdrop-filter: blur(2px);
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  width: 100%;
  max-width: 1400px; /* Increased max-width for better layout */
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);

  &.bulk-update-modal {
    max-width: 600px;
    max-height: 80vh;
  }
}

.modal-header {
  padding: 18px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9fafb;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #1f2937;
    background-color: rgba(107, 114, 128, 0.1);
  }
}

.modal-body {
  padding: 0;
  overflow: auto;
  display: flex;
  height: calc(90vh - 70px); /* Adjust height to account for modal header */
}

.modal-content-container {
  display: flex;
  gap: 20px;
  width: 100%;
}

.bulk-update-container {
  width: 100%;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.bulk-update-header {
  margin-bottom: 12px;

  h4 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 8px 0;
  }

  .bulk-update-description {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
    line-height: 1.4;
  }
}

.bulk-update-form {
  background-color: #f9fafb;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
  width: 100%;
  margin: 0 auto;

  .form-group {
    margin-bottom: 16px;

    label {
      display: block;
      font-weight: 500;
      margin-bottom: 8px;
      color: #374151;
    }

    .status-select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 14px;
      color: #1f2937;
      background-color: white;
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
      }
    }

    .edit-textarea {
      width: 100%;
      min-height: 80px;
      padding: 12px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 14px;
      line-height: 1.5;
      color: #1f2937;
      resize: vertical;
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
      }
    }
  }
}

.test-case-details {
  width: 30%;
  flex-shrink: 0;
  padding-right: 20px;
  border-right: 1px solid #e5e7eb;
}

.history-list {
  width: 70%;
  display: flex;
  gap: 20px;
}

.current-result-container {
  width: 45%;
  flex-shrink: 0;
  padding-right: 20px;
  border-right: 1px solid #e5e7eb;
}

.previous-changes-container {
  width: 55%;
  display: flex;
  flex-direction: column;
}

.test-case-info {
  margin-bottom: 24px;

  .test-case-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e5e7eb;
  }
}

.test-case-properties {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.test-case-property {
  strong {
    display: block;
    margin-bottom: 6px;
    font-size: 14px;
    color: #1f2937;
    font-weight: 600;
  }

  p {
    margin: 0;
    color: #4b5563;
    font-size: 14px;
    line-height: 1.5;
  }

  span {
    display: inline-block;
    padding: 5px 10px;
    background-color: #f3f4f6;
    border-radius: 6px;
    font-size: 13px;
    color: #1f2937;
  }

  &:deep(.tag-badge) { // Style for individual tags
      display: inline-block;
      padding: 5px 10px;
      background-color: #dbeafe; /* Lighter blue background */
      color: #2563eb; /* Darker blue text for better contrast */
      border-radius: 6px;
      font-size: 13px;
      margin-right: 6px;
      margin-bottom: 6px;
      font-weight: 500;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
}

.loading {
  text-align: center;
  padding: 32px;
  color: #4b5563;
  font-size: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;

  &::after {
    content: "";
    display: block;
    width: 40px;
    height: 40px;
    margin-top: 16px;
    border: 3px solid #e5e7eb;
    border-radius: 50%;
    border-top-color: #059669;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #fca5a5;
  font-weight: 500;
}

.empty-state {
  text-align: center;
  padding: 40px 24px;
  color: #6b7280;
  font-size: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px dashed #d1d5db;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.history-item {
  margin-bottom: 16px; /* Increased spacing between items */
  position: relative;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
  }

  &.current-item {
    .history-item-content {
      border-color: #059669;
      box-shadow: 0 4px 12px rgba(5, 150, 105, 0.1);
    }

    &::before {
      content: "Current";
      position: absolute;
      top: 8px;
      right: 8px;
      font-size: 11px;
      font-weight: 600;
      color: #059669;
      background-color: #d1fae5;
      padding: 2px 6px;
      border-radius: 4px;
      z-index: 1; /* Ensure it's above other content */
    }
  }
}

.history-item-content {
  display: flex;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.status-indicator {
  width: 6px;
  flex-shrink: 0;

  &.status-passed {
    background-color: #10b981;
  }

  &.status-failed {
    background-color: #ef4444;
  }

  &.status-blocked {
    background-color: #f59e0b;
  }

  &.status-skipped {
    background-color: #3b82f6;
  }

  &.status-untested {
    background-color: #9ca3af;
  }
}

.history-item-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.history-header {
  background-color: #f9fafb;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e5e7eb;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 16px; /* Rounded pill shape */
  font-size: 13px;
  font-weight: 600;
  text-transform: capitalize;
  transition: all 0.2s ease;

  &.status-passed {
    background-color: rgba(16, 185, 129, 0.1);
    color: #047857;
    border: 1px solid rgba(16, 185, 129, 0.2);
  }

  &.status-failed {
    background-color: rgba(239, 68, 68, 0.1);
    color: #b91c1c;
    border: 1px solid rgba(239, 68, 68, 0.2);
  }

  &.status-blocked {
    background-color: rgba(245, 158, 11, 0.1);
    color: #9a3412;
    border: 1px solid rgba(245, 158, 11, 0.2);
  }

  &.status-skipped {
    background-color: rgba(59, 130, 246, 0.1);
    color: #1d4ed8;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  &.status-untested {
    background-color: rgba(156, 163, 175, 0.1);
    color: #4b5563;
    border: 1px solid rgba(156, 163, 175, 0.2);
  }
}

/* Remove duplicate status classes that were causing style conflicts */

.timestamp {
  font-size: 13px;
  color: #6b7280;
  display: flex;
  align-items: center;

  &::before {
    content: "🕒";
    font-size: 12px;
    margin-right: 4px;
    opacity: 0.7;
  }
}

.history-details {
  padding: 12px 16px 16px;

  .detail-group {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      display: block;
      font-weight: 600;
      color: #4b5563;
      margin-bottom: 6px;
      font-size: 13px;
    }

    .content-container {
      position: relative;

      .truncated-content {
        margin: 0;
        color: #1f2937;
        font-size: 14px;
        line-height: 1.5;
        background-color: white;
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid #e5e7eb;
        max-height: 80px;
        overflow: hidden;
        position: relative;

        &.expanded {
          max-height: none;
        }

        &.empty-content {
          color: #9ca3af;
          font-style: italic;
        }
      }

      .show-more-button {
        background: none;
        border: none;
        color: #2563eb;
        font-size: 12px;
        cursor: pointer;
        padding: 4px 0;
        margin-top: 4px;
        font-weight: 500;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.edit-button {
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid #d1fae5;
  background-color: #ecfdf5;
  color: #059669;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  .edit-icon {
    font-size: 16px;
  }

  &:hover {
    background-color: #d1fae5;
    border-color: #34d399;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.status-select {
  padding: 10px 14px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  background-color: white;
  font-size: 14px;
  color: #1f2937;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;

  &:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
  }
}

.edit-textarea {
  width: 100%;
  padding: 12px 14px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  min-height: 120px;
  resize: vertical;
  font-size: 14px;
  line-height: 1.6;
  color: #1f2937;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
  }
}

.edit-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  margin-bottom: 10px;
  justify-content: flex-end;
}

.status-warning {
  background-color: #fff3cd;
  color: #856404;
  padding: 12px;
  border-radius: 6px;
  margin-top: 16px;
  margin-bottom: 16px;
  border: 1px solid #ffeeba;
  font-size: 14px;
  line-height: 1.5;
}

.status-warning-border {
  border-color: #f59e0b !important;
  background-color: #fffbeb !important;
}

.disabled-button {
  opacity: 0.6;
  cursor: not-allowed !important;
  background-color: #d1d5db !important;
  border-color: #9ca3af !important;
  color: #4b5563 !important;
}

.save-button {
  color: white;
  background-color: #059669;
  border: none;
  padding: 10px 18px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  .save-icon {
    font-size: 16px;
  }

  &:hover {
    background-color: #047857;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  }
}

.cancel-button {
  border: 1px solid #e5e7eb;
  background-color: white;
  color: #4b5563;
  padding: 10px 18px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;

  &:hover {
    background-color: #f3f4f6;
    color: #1f2937;
    transform: translateY(-1px);
  }
}

.defect-section {
  margin-left: 12px;
}

.defect-link {
  display: flex;
  align-items: center;
  gap: 8px;

  .defect-id {
    background-color: #0052cc;
    color: white;
    font-size: 12px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 4px;
  }

  a {
    color: #0052cc;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }
}

.create-defect-button {
  padding: 8px 16px;
  background-color: #0052cc;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  .defect-icon {
    font-size: 16px;
  }

  &:hover {
    background-color: #0747a6;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  }
}

/* Pagination Styles */
.previous-changes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.history-section-title {
  h4 {
    margin: 0;
    font-size: 18px;
    color: #1f2937;
    font-weight: 600;
  }
}

.history-count {
  background-color: #f3f4f6;
  color: #4b5563;
  font-size: 13px;
  padding: 4px 10px;
  border-radius: 16px;
  font-weight: 500;
}

.pagination-container {
  display: flex;
  align-items: center;
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 4px;
  border: 1px solid #e5e7eb;
}

/* Sticky pagination at the bottom of the history column */
.sticky-pagination {
  position: sticky;
  bottom: 0;
  z-index: 2;
  margin-top: 20px;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.08);
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  width: calc(100% - 32px); /* Account for padding */
  left: 16px;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 2px;
}

.pagination-ellipsis {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  color: #6b7280;
  font-size: 14px;
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background-color: transparent;
  color: #1f2937;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  margin: 0 1px;

  &:hover:not(:disabled) {
    background-color: #f3f4f6;
    color: #059669;
  }

  &:disabled {
    color: #d1d5db;
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.pagination-pages {
  display: flex;
  gap: 2px;
  margin: 0 4px;
}

.pagination-page-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background-color: transparent;
  color: #1f2937;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  margin: 0 2px;

  &:hover {
    background-color: #f3f4f6;
  }

  &.active {
    background-color: #059669;
    color: white;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(5, 150, 105, 0.2);
  }
}

.current-result {
  border-left: 4px solid #ffffff;
  padding: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.history-items-list {
  display: flex;
  flex-direction: column;
  max-height: calc(100% - 10px); /* Increased space for pagination */
  overflow-y: auto;
  padding: 0 16px; /* Add padding on both sides */
  margin: 0 -16px; /* Offset the column padding */

  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 8px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }
}

.current-result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e5e7eb;

  h4 {
    margin: 0;
    font-size: 17px;
    color: #1f2937;
    font-weight: 600;
  }

  .edit-button {
    margin: 0;
  }
}
/* Three Column Layout Styles */
.three-column-layout {
  display: flex;
  flex-direction: row; /* Ensures horizontal layout */
  gap: 0; /* Remove gap, we'll use borders instead */
  width: 100%;
  height: 100%;
}

/* Style for each column to ensure they take appropriate width */
.test-case-details-column,
.history-column,
.update-result-column {
  min-width: 0; /* Allows columns to shrink below content size if needed */
  overflow-y: auto; /* Scrollable content if needed */
  padding: 20px;
  background-color: white;
  position: relative;
  height: 100%;
}

/* Column proportions */
.test-case-details-column {
  flex: 3.2; /* Takes 30% of space */
  border-right: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.history-column {
  flex: 3.5; /* Takes 40% of space */
  border-right: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: #f9fafb; /* Light background for better contrast */

  background-position: -1px -1px;
}

.update-result-column {
  flex: 3.2; /* Takes 30% of space */
  background-color: #f9fafb;
}

/* Modal container to ensure proper sizing */
.modal-content {
  width: 95vw; /* Takes 95% of viewport width */
  max-width: 1600px; /* Maximum width */
  max-height: 90vh; /* Maximum height */
  overflow: hidden; /* Hide overflow */
}

.modal-body {
  overflow: hidden; /* Hide overflow */
  height: calc(90vh - 70px); /* Adjust height to account for modal header */
}

/* Responsive behavior for smaller screens */
@media (max-width: 1200px) {
  .three-column-layout {
    flex-direction: column; /* Stack vertically on smaller screens */
  }

  .test-case-details-column,
  .history-column,
  .update-result-column {
    flex: none;
    width: 100%; /* Full width on smaller screens */
    height: auto;
    max-height: 500px;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 0;
    padding: 16px;
  }

  .update-result-column {
    border-bottom: none;
  }

  .modal-body {
    overflow-y: auto;
    height: auto;
    max-height: calc(90vh - 70px);
  }
}

.form-group {
  margin-bottom: 20px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #1f2937;
    font-size: 14px;
  }
}

.result-view-mode {
  padding: 20px;
}

.result-edit-mode {
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 0 8px 8px 0;
}

.result-card {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-row {
  display: flex;
  align-items: flex-start;
  padding-bottom: 12px;
  border-bottom: 1px solid #f3f4f6;

  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.result-label {
  width: 120px;
  flex-shrink: 0;
  font-weight: 500;
  color: #4b5563;
  font-size: 14px;
}

.result-value {
  flex: 1;
  font-size: 14px;
  color: #1f2937;

  &.result-text {
    background-color: #f9fafb;
    padding: 10px 12px;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    line-height: 1.5;
  }
}

/* Artifact Links Styles */
.artifact-links {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  flex-wrap: wrap;
}

.artifact-link {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  &.screenshot-link {
    background-color: #dbeafe;
    color: #1d4ed8;
    border: 1px solid #bfdbfe;

    &:hover {
      background-color: #bfdbfe;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(29, 78, 216, 0.2);
    }
  }

  &.video-link {
    background-color: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;

    &:hover {
      background-color: #fde68a;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(146, 64, 14, 0.2);
    }
  }
}

/* Artifact Modal Styles */
.artifact-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.artifact-modal-content {
  background-color: white;
  border-radius: 12px;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.artifact-modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9fafb;

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
  }
}

.artifact-close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #1f2937;
    background-color: rgba(107, 114, 128, 0.1);
  }
}

.artifact-modal-body {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: auto;
}

.artifact-screenshot {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.artifact-video {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>