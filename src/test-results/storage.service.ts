import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Storage } from '@google-cloud/storage';
import { Config } from '../config';

@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private storage: Storage;
  private bucketName: string;

  constructor(private configService: ConfigService) {
    this.bucketName = Config.GCP_BUCKET_NAME;
    
    // Initialize Google Cloud Storage
    this.storage = new Storage({
      projectId: Config.GCP_PROJECT_ID,
      credentials: {
        client_email: Config.GCP_CLIENT_EMAIL,
        private_key: Config.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
    });
  }

  /**
   * Upload logs to Google Cloud Storage (Latest only per test case)
   * @param logsData - Object containing projectId, tcId, testRunId, testResultId
   * @param logs - Array of log messages
   * @returns Promise<string> - The GCS URL
   */
  async uploadLogs(logsData: { projectId: string; tcId: string; testRunId: string; testResultId: string }, logs: string[]): Promise<string> {
    try {
      // Use tcId instead of testResultId to store only latest per test case
      const fileName = `test-results/logs/${logsData.projectId}/latest/${logsData.tcId}/logs.json`;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      // Delete existing file if it exists (to replace with latest)
      try {
        await file.delete();
        this.logger.log(`Deleted previous logs for test case: ${logsData.tcId}`);
      } catch (deleteError) {
        // File doesn't exist, which is fine for first upload
        this.logger.log(`No previous logs found for test case: ${logsData.tcId}`);
      }

      const logData = {
        timestamp: new Date().toISOString(),
        logs,
        metadata: {
          totalLogs: logs.length,
          uploadedAt: new Date().toISOString(),
        },
      };

      // Upload the logs as JSON
      await file.save(JSON.stringify(logData, null, 2), {
        metadata: {
          contentType: 'application/json',
        },
      });

      this.logger.log(`Logs uploaded successfully for test result: ${logsData.tcId}`);
      return `gs://${this.bucketName}/${fileName}`;
    } catch (error) {
      this.logger.error(`Failed to upload logs for test result ${logsData.tcId}:`, error);
      throw new Error(`Failed to upload logs: ${error}`);
    }
  }

  /**
   * Download logs from Google Cloud Storage
   * @param logsUrl - The GCS URL
   * @returns Promise<string[]> - Array of log messages
   */
  async downloadLogs(logsUrl: string): Promise<string[]> {
    try {
      // Extract file path from GCS URL
      const filePath = logsUrl.replace(`gs://${this.bucketName}/`, '');
      const file = this.storage.bucket(this.bucketName).file(filePath);

      const [contents] = await file.download();
      const logData = JSON.parse(contents.toString());

      return logData.logs || [];
    } catch (error) {
      this.logger.error(`Failed to download logs from ${logsUrl}:`, error);
      throw new Error(`Failed to download logs: ${error}`);
    }
  }

  /**
   * Delete logs from Google Cloud Storage
   * @param logsUrl - The GCS URL
   * @returns Promise<void>
   */
  async deleteLogs(logsUrl: string): Promise<void> {
    try {
      // Extract file path from GCS URL
      const filePath = logsUrl.replace(`gs://${this.bucketName}/`, '');
      const file = this.storage.bucket(this.bucketName).file(filePath);

      await file.delete();
      this.logger.log(`Logs deleted successfully: ${logsUrl}`);
    } catch (error) {
      this.logger.error(`Failed to delete logs from ${logsUrl}:`, error);
      throw new Error(`Failed to delete logs: ${error}`);
    }
  }

  /**
   * Check if the bucket exists and is accessible
   * @returns Promise<boolean>
   */
  async checkBucketAccess(): Promise<boolean> {
    try {
      const bucket = this.storage.bucket(this.bucketName);
      const [exists] = await bucket.exists();

      if (!exists) {
        this.logger.warn(`Bucket ${this.bucketName} does not exist`);
        return false;
      }

      // Try to list files to check permissions
      await bucket.getFiles({ maxResults: 1 });
      this.logger.log(`Bucket ${this.bucketName} is accessible`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to access bucket ${this.bucketName}:`, error);
      return false;
    }
  }

  /**
   * Upload screenshot to Google Cloud Storage (Latest only per test case)
   * @param projectId - The project ID
   * @param testRunId - The test run ID (for compatibility, but not used in path)
   * @param tcId - The test case ID (used for storage path)
   * @param screenshotBuffer - The screenshot file buffer
   * @param contentType - The screenshot content type (e.g., 'image/png')
   * @returns Promise<string> - The GCS URL
   */
  async uploadScreenshot(projectId: string, testRunId: string, tcId: string, screenshotBuffer: Buffer, contentType: string = 'image/png'): Promise<string> {
    try {
      // Use tcId instead of testResultId to store only latest per test case
      const fileName = `test-results/screenshots/${projectId}/latest/${tcId}/screenshot.png`;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      // Delete existing file if it exists (to replace with latest)
      try {
        await file.delete();
        this.logger.log(`Deleted previous screenshot for test case: ${tcId}`);
      } catch (deleteError) {
        // File doesn't exist, which is fine for first upload
        this.logger.log(`No previous screenshot found for test case: ${tcId}`);
      }

      // Upload the screenshot
      await file.save(screenshotBuffer, {
        metadata: {
          contentType: contentType,
        },
      });

      this.logger.log(`Screenshot uploaded successfully for test case: ${tcId}`);
      return `gs://${this.bucketName}/${fileName}`;
    } catch (error) {
      this.logger.error(`Failed to upload screenshot for test case ${tcId}:`, error);
      throw new Error(`Failed to upload screenshot: ${error}`);
    }
  }

  /**
   * Upload video to Google Cloud Storage (Latest only per test case)
   * @param projectId - The project ID
   * @param testRunId - The test run ID (for compatibility, but not used in path)
   * @param tcId - The test case ID
   * @param videoBuffer - The video file buffer
   * @param contentType - The video content type (e.g., 'video/webm')
   * @returns Promise<string> - The GCS URL
   */
  async uploadVideo(projectId: string, testRunId: string, tcId: string, videoBuffer: Buffer, contentType: string = 'video/webm'): Promise<string> {
    try {
      // Use tcId instead of testRunId/testResultId to store only latest per test case
      const fileName = `test-results/videos/${projectId}/latest/${tcId}/video.webm`;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      // Delete existing file if it exists (to replace with latest)
      try {
        await file.delete();
        this.logger.log(`Deleted previous video for test case: ${tcId}`);
      } catch (deleteError) {
        // File doesn't exist, which is fine for first upload
        this.logger.log(`No previous video found for test case: ${tcId}`);
      }

      // Upload the video
      await file.save(videoBuffer, {
        metadata: {
          contentType: contentType,
        },
      });

      this.logger.log(`Video uploaded successfully for test result: ${tcId}`);
      return `gs://${this.bucketName}/${fileName}`;
    } catch (error) {
      this.logger.error(`Failed to upload video for test result ${tcId}:`, error);
      throw new Error(`Failed to upload video: ${error}`);
    }
  }

  /**
   * Delete all test run data from Google Cloud Storage
   * @param projectId - The project ID
   * @param testRunId - The test run ID to delete
   * @returns Promise<void>
   */
  async deleteTestRunData(projectId: string, testRunId: string): Promise<void> {
    try {
      this.logger.log(`Starting cleanup of test run data for: ${testRunId}`);

      // Define the prefixes for all test run related files
      const prefixes = [
        `test-results/videos/${projectId}/${testRunId}/`,
        `test-results/screenshots/${projectId}/${testRunId}/`,
        `test-results/logs/${projectId}/${testRunId}/`
      ];

      let totalDeleted = 0;

      // Delete files for each prefix
      for (const prefix of prefixes) {
        try {
          this.logger.log(`Deleting files with prefix: ${prefix}`);

          // List all files with this prefix
          const [files] = await this.storage.bucket(this.bucketName).getFiles({
            prefix: prefix
          });

          if (files.length > 0) {
            this.logger.log(`Found ${files.length} files to delete with prefix: ${prefix}`);

            // Delete all files
            await Promise.all(files.map(file => file.delete()));
            totalDeleted += files.length;

            this.logger.log(`Successfully deleted ${files.length} files with prefix: ${prefix}`);
          } else {
            this.logger.log(`No files found with prefix: ${prefix}`);
          }
        } catch (error) {
          this.logger.error(`Error deleting files with prefix ${prefix}:`, error);
          // Continue with other prefixes even if one fails
        }
      }

      this.logger.log(`Test run cleanup completed. Total files deleted: ${totalDeleted}`);
    } catch (error) {
      this.logger.error(`Failed to cleanup test run data for ${testRunId}:`, error);
      throw new Error(`Failed to cleanup test run data: ${error}`);
    }
  }

  /**
   * Get a signed URL for a file in Google Cloud Storage
   * @param gcsUrl - The GCS URL (gs://bucket-name/path/to/file)
   * @returns Promise<string> - The signed URL for direct access
   */
  async getSignedUrl(gcsUrl: string): Promise<string> {
    try {
      // Extract file path from GCS URL
      const filePath = gcsUrl.replace(`gs://${this.bucketName}/`, '');
      const file = this.storage.bucket(this.bucketName).file(filePath);

      // Generate a signed URL that expires in 1 hour
      const [signedUrl] = await file.getSignedUrl({
        version: 'v4',
        action: 'read',
        expires: Date.now() + 60 * 60 * 1000, // 1 hour
      });

      return signedUrl;
    } catch (error) {
      this.logger.error(`Failed to generate signed URL for ${gcsUrl}:`, error);
      throw new Error(`Failed to generate signed URL: ${error}`);
    }
  }
}
